
/* Global Styles */
:root {
    --primary-color: #ffffff;
    --secondary-color: #f5f5f5;
    --accent-color: #000000;
    --text-color: #333333;
    --border-color: #e0e0e0;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Tajawal', sans-serif;
    background-color: var(--primary-color);
    color: var(--text-color);
    direction: rtl;
    overflow-x: hidden;
}

/* App Container */
.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

/* Header */
.app-header {
    background-color: var(--primary-color);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow);
    padding: 15px 20px;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo i {
    font-size: 28px;
    color: var(--accent-color);
}

.logo h1 {
    font-size: 24px;
    font-weight: 700;
    color: var(--accent-color);
}

.header-actions {
    display: flex;
    gap: 10px;
}

/* Buttons */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--accent-color);
    color: var(--accent-color);
}

.btn-outline:hover {
    background-color: var(--accent-color);
    color: var(--primary-color);
}

.btn-primary {
    background-color: var(--accent-color);
    color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #333333;
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: var(--accent-color);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background-color: #e9e9e9;
}

/* Main Content */
.app-main {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.main-container {
    display: flex;
    flex: 1;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
    gap: 20px;
    padding: 20px;
}

/* Sidebar */
.sidebar {
    width: 250px;
    background-color: var(--secondary-color);
    border-radius: 8px;
    padding: 15px;
    overflow-y: auto;
    box-shadow: var(--shadow);
}

.sidebar-section {
    margin-bottom: 25px;
}

.sidebar-section h2 {
    font-size: 18px;
    margin-bottom: 15px;
    color: var(--accent-color);
    padding-bottom: 8px;
    border-bottom: 1px solid var(--border-color);
}

.barcode-types {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.type-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
}

.type-item:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.type-item.active {
    background-color: var(--accent-color);
    color: var(--primary-color);
}

.type-item i {
    font-size: 18px;
}

.type-item span {
    font-size: 14px;
}

.settings {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.setting-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.setting-item label {
    font-size: 14px;
    font-weight: 500;
}

.setting-item input[type="range"] {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: var(--border-color);
    outline: none;
    -webkit-appearance: none;
}

.setting-item input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--accent-color);
    cursor: pointer;
}

.setting-item input[type="range"]::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--accent-color);
    cursor: pointer;
}

.setting-item span {
    font-size: 14px;
    text-align: left;
}

/* Content Area */
.content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.input-section {
    background-color: var(--primary-color);
    border-radius: 8px;
    padding: 20px;
    box-shadow: var(--shadow);
}

.input-group {
    margin-bottom: 15px;
}

.input-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    font-size: 14px;
}

textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    font-family: 'Tajawal', sans-serif;
    resize: vertical;
    min-height: 100px;
}

textarea:focus {
    outline: none;
    border-color: var(--accent-color);
}

.batch-input {
    margin-bottom: 15px;
}

.mode-toggle {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.mode-btn {
    flex: 1;
    padding: 8px 16px;
    border: 1px solid var(--border-color);
    background-color: var(--primary-color);
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
}

.mode-btn.active {
    background-color: var(--accent-color);
    color: var(--primary-color);
    border-color: var(--accent-color);
}

.action-buttons {
    display: flex;
    gap: 10px;
}

.preview-section {
    background-color: var(--primary-color);
    border-radius: 8px;
    padding: 20px;
    box-shadow: var(--shadow);
    flex: 1;
    display: flex;
    flex-direction: column;
}

.preview-section h2 {
    font-size: 18px;
    margin-bottom: 15px;
    color: var(--accent-color);
}

.preview-container {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px dashed var(--border-color);
    border-radius: 8px;
    min-height: 200px;
    background-color: #fafafa;
}

.empty-preview {
    text-align: center;
    color: #999;
}

.empty-preview i {
    font-size: 48px;
    margin-bottom: 10px;
    display: block;
}

.export-options {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

.export-options h3 {
    font-size: 16px;
    margin-bottom: 15px;
    color: var(--accent-color);
}

.format-buttons {
    display: flex;
    gap: 10px;
}

.export-btn {
    flex: 1;
    padding: 10px;
    border: 1px solid var(--border-color);
    background-color: var(--primary-color);
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.export-btn:hover {
    background-color: var(--secondary-color);
}

.export-btn i {
    font-size: 20px;
}

/* Footer */
.app-footer {
    background-color: var(--secondary-color);
    border-top: 1px solid var(--border-color);
    padding: 10px 20px;
    text-align: center;
    font-size: 14px;
    color: #666;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: var(--primary-color);
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    box-shadow: var(--shadow);
    overflow: hidden;
}

.modal-header {
    background-color: var(--accent-color);
    color: var(--primary-color);
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    font-size: 20px;
}

.close {
    font-size: 24px;
    cursor: pointer;
    color: var(--primary-color);
}

.close:hover {
    opacity: 0.8;
}

.modal-body {
    padding: 20px;
}

.modal-body p {
    margin-bottom: 15px;
    line-height: 1.6;
}

.modal-body h3 {
    margin-top: 20px;
    margin-bottom: 10px;
    font-size: 18px;
}

.modal-body ul {
    list-style-position: inside;
    margin-bottom: 15px;
}

.modal-body li {
    margin-bottom: 8px;
    line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-container {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        max-height: 200px;
    }

    .action-buttons {
        flex-direction: column;
    }

    .format-buttons {
        flex-direction: column;
    }
}
