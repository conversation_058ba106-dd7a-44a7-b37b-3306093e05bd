
// Global variables
let currentFormat = 'QR Code';
let currentWidth = 2;
let currentHeight = 100;
let currentMode = 'single'; // 'single' or 'batch'
let generatedBarcodes = [];

// DOM elements
const barcodeDataInput = document.getElementById('barcode-data');
const batchDataInput = document.getElementById('batch-data');
const generateBtn = document.getElementById('generate-btn');
const previewBtn = document.getElementById('preview-btn');
const previewContainer = document.getElementById('preview-container');
const exportOptions = document.getElementById('export-options');
const exportPdfBtn = document.getElementById('export-pdf');
const widthSlider = document.getElementById('barcode-width');
const heightSlider = document.getElementById('barcode-height');
const widthValue = document.getElementById('width-value');
const heightValue = document.getElementById('height-value');
const singleModeBtn = document.getElementById('single-mode');
const batchModeBtn = document.getElementById('batch-mode');
const batchInputSection = document.getElementById('batch-input-section');
const typeItems = document.querySelectorAll('.type-item');
const exportBtns = document.querySelectorAll('.export-btn');
const aboutBtn = document.getElementById('about-btn');
const aboutModal = document.getElementById('about-modal');
const closeModal = document.querySelector('.close');

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    // Set up event listeners
    setupEventListeners();

    // Load supported barcode formats
    loadBarcodeFormats();
});

// Set up all event listeners
function setupEventListeners() {
    // Generate button
    generateBtn.addEventListener('click', generateBarcode);

    // Preview button
    previewBtn.addEventListener('click', previewBarcode);

    // Width slider
    widthSlider.addEventListener('input', (e) => {
        currentWidth = parseFloat(e.target.value);
        widthValue.textContent = currentWidth;
    });

    // Height slider
    heightSlider.addEventListener('input', (e) => {
        currentHeight = parseInt(e.target.value);
        heightValue.textContent = currentHeight;
    });

    // Mode toggle buttons
    singleModeBtn.addEventListener('click', () => setMode('single'));
    batchModeBtn.addEventListener('click', () => setMode('batch'));

    // Barcode type selection
    typeItems.forEach(item => {
        item.addEventListener('click', () => selectBarcodeType(item));
    });

    // Export buttons
    exportBtns.forEach(btn => {
        btn.addEventListener('click', () => exportBarcode(btn.dataset.format));
    });

    // About modal
    aboutBtn.addEventListener('click', () => {
        aboutModal.style.display = 'flex';
    });

    closeModal.addEventListener('click', () => {
        aboutModal.style.display = 'none';
    });

    // Close modal when clicking outside
    window.addEventListener('click', (e) => {
        if (e.target === aboutModal) {
            aboutModal.style.display = 'none';
        }
    });
}

// Load supported barcode formats
async function loadBarcodeFormats() {
    try {
        const formats = await eel.get_barcode_formats()();
        // Formats are already defined in HTML, but we could use this data if needed
    } catch (error) {
        console.error('Error loading barcode formats:', error);
        showError('حدث خطأ في تحميل أنواع الباركود');
    }
}

// Set the application mode (single or batch)
function setMode(mode) {
    currentMode = mode;

    if (mode === 'single') {
        singleModeBtn.classList.add('active');
        batchModeBtn.classList.remove('active');
        batchInputSection.style.display = 'none';
        barcodeDataInput.style.display = 'block';
    } else {
        singleModeBtn.classList.remove('active');
        batchModeBtn.classList.add('active');
        batchInputSection.style.display = 'block';
        barcodeDataInput.style.display = 'none';
    }
}

// Select a barcode type
function selectBarcodeType(item) {
    typeItems.forEach(i => i.classList.remove('active'));
    item.classList.add('active');
    currentFormat = item.dataset.type;
}

// Generate barcode(s)
async function generateBarcode() {
    try {
        // Show loading state
        generateBtn.disabled = true;
        generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التوليد...';

        let result;

        if (currentMode === 'single') {
            const data = barcodeDataInput.value.trim();
            if (!data) {
                showError('الرجاء إدخال البيانات');
                resetGenerateButton();
                return;
            }

            result = await eel.generate_barcode(data, currentFormat, currentWidth, currentHeight)();
        } else {
            const batchData = batchDataInput.value.trim();
            if (!batchData) {
                showError('الرجاء إدخال البيانات الدفعية');
                resetGenerateButton();
                return;
            }

            const dataLines = batchData.split('\n').filter(line => line.trim());
            if (dataLines.length === 0) {
                showError('الرجاء إدخال بيانات صالحة');
                resetGenerateButton();
                return;
            }

            result = await eel.generate_batch_barcode(dataLines, currentFormat, currentWidth, currentHeight)();
        }

        if (result.success) {
            generatedBarcodes = Array.isArray(result) ? result : [result];
            showSuccess('تم توليد الباركود بنجاح');
            previewBtn.style.display = 'inline-flex';
        } else {
            showError(result.error || 'حدث خطأ أثناء توليد الباركود');
        }
    } catch (error) {
        console.error('Error generating barcode:', error);
        showError('حدث خطأ في الاتصال بالخادم');
    } finally {
        resetGenerateButton();
    }
}

// Preview the generated barcode
async function previewBarcode() {
    if (!generatedBarcodes || generatedBarcodes.length === 0) {
        showError('لا يوجد باركود معروض لعرضه');
        return;
    }

    try {
        // Clear preview container
        previewContainer.innerHTML = '';

        // Create a container for barcodes
        const barcodesContainer = document.createElement('div');
        barcodesContainer.className = 'barcodes-container';

        // Add each barcode to the container
        for (const barcode of generatedBarcodes) {
            if (barcode.success && barcode.path) {
                const barcodeElement = document.createElement('div');
                barcodeElement.className = 'barcode-item';

                const img = document.createElement('img');
                img.src = 'file:///' + barcode.path;
                img.alt = 'باركود ' + currentFormat;

                const label = document.createElement('div');
                label.className = 'barcode-label';
                label.textContent = currentFormat;

                barcodeElement.appendChild(img);
                barcodeElement.appendChild(label);
                barcodesContainer.appendChild(barcodeElement);
            }
        }

        // Add container to preview
        previewContainer.appendChild(barcodesContainer);

        // Show export options
        exportOptions.style.display = 'block';

        // Scroll to preview
        previewContainer.scrollIntoView({ behavior: 'smooth' });
    } catch (error) {
        console.error('Error previewing barcode:', error);
        showError('حدث خطأ أثناء عرض الباركود');
    }
}

// Export the barcode
async function exportBarcode(format) {
    if (!generatedBarcodes || generatedBarcodes.length === 0) {
        showError('لا يوجد باركود للتصدير');
        return;
    }

    try {
        // Get the paths of successful barcodes
        const paths = generatedBarcodes
            .filter(barcode => barcode.success && barcode.path)
            .map(barcode => barcode.path);

        if (paths.length === 0) {
            showError('لا يوجد باركود صالح للتصدير');
            return;
        }

        if (format === 'PDF') {
            // Export to PDF
            const result = await eel.export_to_pdf(paths)();

            if (result.success) {
                // Open the PDF in the default viewer
                window.open('file:///' + result.path);
                showSuccess('تم تصدير الباركود إلى PDF بنجاح. الملف تم حفظه في: ' + result.path);
            } else {
                showError(result.error || 'حدث خطأ أثناء تصدير PDF');
            }
        } else {
            // For PNG and SVG, open each file
            paths.forEach(path => {
                window.open('file:///' + path);
            });

            showSuccess(`تم تصدير ${paths.length} باركود بنجاح`);
        }
    } catch (error) {
        console.error('Error exporting barcode:', error);
        showError('حدث خطأ أثناء تصدير الباركود');
    }
}

// Reset generate button state
function resetGenerateButton() {
    generateBtn.disabled = false;
    generateBtn.innerHTML = '<i class="fas fa-magic"></i> توليد الباركود';
}

// Show success message
function showSuccess(message) {
    // Create a toast notification
    const toast = document.createElement('div');
    toast.className = 'toast success';
    toast.textContent = message;

    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);

    // Remove after 3 seconds
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

// Show error message
function showError(message) {
    // Create a toast notification
    const toast = document.createElement('div');
    toast.className = 'toast error';
    toast.textContent = message;

    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);

    // Remove after 3 seconds
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

// Add toast styles to document
const style = document.createElement('style');
style.textContent = `
    .toast {
        position: fixed;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        padding: 12px 24px;
        border-radius: 4px;
        color: white;
        font-weight: 500;
        z-index: 1001;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .toast.success {
        background-color: #4caf50;
    }

    .toast.error {
        background-color: #f44336;
    }

    .toast.show {
        opacity: 1;
    }

    .barcodes-container {
        display: flex;
        flex-direction: column;
        gap: 20px;
        width: 100%;
    }

    .barcode-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }

    .barcode-item img {
        max-width: 100%;
        height: auto;
        border: 1px solid var(--border-color);
        border-radius: 4px;
    }

    .barcode-label {
        font-size: 14px;
        font-weight: 500;
        color: var(--text-color);
    }
`;
document.head.appendChild(style);
