
/* Global Styles */
:root {
    --primary-color: #ffffff;
    --secondary-color: #f5f5f5;
    --accent-color: #3498db;
    --accent-dark: #2980b9;
    --text-color: #333333;
    --border-color: #e0e0e0;
    --shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
    --gradient: linear-gradient(135deg, #3498db, #2980b9);
    --error-color: #e74c3c;
    --success-color: #2ecc71;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    background-color: var(--primary-color);
    color: var(--text-color);
    direction: rtl;
    overflow-x: hidden;
    background-image: linear-gradient(120deg, #f5f7fa 0%, #e4eff9 100%);
    min-height: 100vh;
}

/* Authentication Container */
.auth-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
    background-image: linear-gradient(120deg, #f5f7fa 0%, #e4eff9 100%);
}

.auth-card {
    background-color: var(--primary-color);
    border-radius: 12px;
    box-shadow: var(--shadow);
    width: 100%;
    max-width: 450px;
    overflow: hidden;
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.auth-header {
    background: var(--gradient);
    color: white;
    padding: 30px 20px;
    text-align: center;
}

.auth-header .logo {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.auth-header .logo i {
    font-size: 32px;
}

.auth-header .logo h1 {
    font-size: 24px;
    font-weight: 700;
}

.auth-header p {
    font-size: 16px;
    opacity: 0.9;
}

.auth-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
}

.auth-tab {
    flex: 1;
    padding: 15px;
    background: none;
    border: none;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    color: var(--text-color);
    transition: var(--transition);
    position: relative;
}

.auth-tab.active {
    color: var(--accent-color);
}

.auth-tab.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    right: 0;
    left: 0;
    height: 3px;
    background: var(--gradient);
}

.auth-form {
    padding: 25px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    font-size: 14px;
    color: var(--text-color);
}

.form-group input {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 16px;
    font-family: 'Tajawal', sans-serif;
    transition: var(--transition);
}

.form-group input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

.code-input-container {
    position: relative;
}

.code-input-container input {
    text-align: center;
    font-size: 24px;
    font-weight: 700;
    letter-spacing: 8px;
    padding: 15px;
}

.btn-block {
    width: 100%;
    padding: 12px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 6px;
    margin-top: 10px;
}

.auth-footer {
    padding: 15px;
    text-align: center;
    font-size: 14px;
    color: #777;
    border-top: 1px solid var(--border-color);
}

/* App Container */
.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    animation: fadeIn 0.5s ease;
}

/* Header */
.app-header {
    background-color: var(--primary-color);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow);
    padding: 15px 20px;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo i {
    font-size: 28px;
    color: var(--accent-color);
}

.logo h1 {
    font-size: 24px;
    font-weight: 700;
    color: var(--accent-color);
}

.header-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 15px;
    background-color: var(--secondary-color);
    border-radius: 20px;
    font-weight: 500;
}

.user-info i {
    font-size: 18px;
    color: var(--accent-color);
}

/* Buttons */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--accent-color);
    color: var(--accent-color);
}

.btn-outline:hover {
    background-color: var(--accent-color);
    color: var(--primary-color);
}

.btn-primary {
    background: var(--gradient);
    color: var(--primary-color);
    box-shadow: 0 2px 5px rgba(52, 152, 219, 0.3);
}

.btn-primary:hover {
    background: var(--accent-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(52, 152, 219, 0.4);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: var(--accent-color);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background-color: #e9e9e9;
}

/* Main Content */
.app-main {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.main-container {
    display: flex;
    flex: 1;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
    gap: 20px;
    padding: 20px;
}

/* Sidebar */
.sidebar {
    width: 250px;
    background-color: var(--primary-color);
    border-radius: 10px;
    padding: 15px;
    overflow-y: auto;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.sidebar-section {
    margin-bottom: 25px;
}

.sidebar-section h2 {
    font-size: 18px;
    margin-bottom: 15px;
    color: var(--accent-color);
    padding-bottom: 8px;
    border-bottom: 1px solid var(--border-color);
}

.barcode-types {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.type-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
}

.type-item:hover {
    background-color: rgba(52, 152, 219, 0.1);
}

.type-item.active {
    background: var(--gradient);
    color: var(--primary-color);
    box-shadow: 0 2px 5px rgba(52, 152, 219, 0.2);
}

.type-item i {
    font-size: 18px;
}

.type-item span {
    font-size: 14px;
}

.settings {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.setting-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.setting-item label {
    font-size: 14px;
    font-weight: 500;
}

.setting-item input[type="range"] {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: var(--border-color);
    outline: none;
    -webkit-appearance: none;
}

.setting-item input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--accent-color);
    cursor: pointer;
}

.setting-item input[type="range"]::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--accent-color);
    cursor: pointer;
}

.setting-item span {
    font-size: 14px;
    text-align: left;
}

/* Content Area */
.content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.input-section {
    background-color: var(--primary-color);
    border-radius: 10px;
    padding: 20px;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.input-group {
    margin-bottom: 15px;
}

.input-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    font-size: 14px;
}

textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    font-family: 'Tajawal', sans-serif;
    resize: vertical;
    min-height: 100px;
    transition: var(--transition);
}

textarea:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

.batch-input {
    margin-bottom: 15px;
}

.mode-toggle {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.mode-btn {
    flex: 1;
    padding: 8px 16px;
    border: 1px solid var(--border-color);
    background-color: var(--primary-color);
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
}

.mode-btn.active {
    background: var(--gradient);
    color: var(--primary-color);
    border-color: var(--accent-color);
}

.action-buttons {
    display: flex;
    gap: 10px;
}

.preview-section {
    background-color: var(--primary-color);
    border-radius: 10px;
    padding: 20px;
    box-shadow: var(--shadow);
    flex: 1;
    display: flex;
    flex-direction: column;
    border: 1px solid var(--border-color);
}

.preview-section h2 {
    font-size: 18px;
    margin-bottom: 15px;
    color: var(--accent-color);
}

.preview-container {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px dashed var(--border-color);
    border-radius: 8px;
    min-height: 200px;
    background-color: #fafafa;
}

.empty-preview {
    text-align: center;
    color: #999;
}

.empty-preview i {
    font-size: 48px;
    margin-bottom: 10px;
    display: block;
}

.export-options {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

.export-options h3 {
    font-size: 16px;
    margin-bottom: 15px;
    color: var(--accent-color);
}

.format-buttons {
    display: flex;
    gap: 10px;
}

.export-btn {
    flex: 1;
    padding: 10px;
    border: 1px solid var(--border-color);
    background-color: var(--primary-color);
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.export-btn:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.export-btn i {
    font-size: 20px;
}

/* Footer */
.app-footer {
    background-color: var(--secondary-color);
    border-top: 1px solid var(--border-color);
    padding: 10px 20px;
    text-align: center;
    font-size: 14px;
    color: #666;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: var(--primary-color);
    border-radius: 10px;
    width: 90%;
    max-width: 500px;
    box-shadow: var(--shadow);
    overflow: hidden;
    animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
    from { opacity: 0; transform: scale(0.9); }
    to { opacity: 1; transform: scale(1); }
}

.modal-header {
    background: var(--gradient);
    color: var(--primary-color);
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    font-size: 20px;
}

.close {
    font-size: 24px;
    cursor: pointer;
    color: var(--primary-color);
}

.close:hover {
    opacity: 0.8;
}

.modal-body {
    padding: 20px;
}

.modal-body p {
    margin-bottom: 15px;
    line-height: 1.6;
}

.modal-body h3 {
    margin-top: 20px;
    margin-bottom: 10px;
    font-size: 18px;
}

.modal-body ul {
    list-style-position: inside;
    margin-bottom: 15px;
}

.modal-body li {
    margin-bottom: 8px;
    line-height: 1.5;
}

/* Toast Notifications */
.toast {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    padding: 12px 24px;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    z-index: 1001;
    opacity: 0;
    transition: opacity 0.3s ease;
    box-shadow: var(--shadow);
}

.toast.success {
    background-color: var(--success-color);
}

.toast.error {
    background-color: var(--error-color);
}

.toast.show {
    opacity: 1;
}

.barcodes-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
}

.barcode-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.barcode-item img {
    max-width: 100%;
    height: auto;
    border: 1px solid var(--border-color);
    border-radius: 6px;
}

.barcode-label {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-container {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        max-height: 200px;
    }

    .action-buttons {
        flex-direction: column;
    }

    .format-buttons {
        flex-direction: column;
    }

    .header-content {
        flex-direction: column;
        gap: 15px;
    }

    .header-actions {
        width: 100%;
        justify-content: space-between;
    }
}
