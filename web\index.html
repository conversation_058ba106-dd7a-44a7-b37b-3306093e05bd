
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Elashrafy Editor - مولِّد الباركود الذكي</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-barcode"></i>
                    <h1>Elashrafy Editor</h1>
                </div>
                <div class="header-actions">
                    <button id="about-btn" class="btn btn-outline">
                        <i class="fas fa-info-circle"></i> حول
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="app-main">
            <div class="main-container">
                <!-- Sidebar -->
                <aside class="sidebar">
                    <div class="sidebar-section">
                        <h2>أنواع الباركود</h2>
                        <div class="barcode-types">
                            <div class="type-item active" data-type="QR Code">
                                <i class="fas fa-qrcode"></i>
                                <span>QR Code</span>
                            </div>
                            <div class="type-item" data-type="Code 128">
                                <i class="fas fa-barcode"></i>
                                <span>Code 128</span>
                            </div>
                            <div class="type-item" data-type="EAN-13">
                                <i class="fas fa-barcode"></i>
                                <span>EAN-13</span>
                            </div>
                            <div class="type-item" data-type="EAN-8">
                                <i class="fas fa-barcode"></i>
                                <span>EAN-8</span>
                            </div>
                            <div class="type-item" data-type="UPC-A">
                                <i class="fas fa-barcode"></i>
                                <span>UPC-A</span>
                            </div>
                            <div class="type-item" data-type="Code 39">
                                <i class="fas fa-barcode"></i>
                                <span>Code 39</span>
                            </div>
                            <div class="type-item" data-type="ITF">
                                <i class="fas fa-barcode"></i>
                                <span>ITF</span>
                            </div>
                            <div class="type-item" data-type="MSI">
                                <i class="fas fa-barcode"></i>
                                <span>MSI</span>
                            </div>
                            <div class="type-item" data-type="Pharmacode">
                                <i class="fas fa-pills"></i>
                                <span>Pharmacode</span>
                            </div>
                        </div>
                    </div>

                    <div class="sidebar-section">
                        <h2>الإعدادات</h2>
                        <div class="settings">
                            <div class="setting-item">
                                <label for="barcode-width">العرض:</label>
                                <input type="range" id="barcode-width" min="1" max="5" value="2" step="0.5">
                                <span id="width-value">2</span>
                            </div>
                            <div class="setting-item">
                                <label for="barcode-height">الارتفاع:</label>
                                <input type="range" id="barcode-height" min="50" max="200" value="100" step="10">
                                <span id="height-value">100</span>
                            </div>
                        </div>
                    </div>
                </aside>

                <!-- Main Content Area -->
                <div class="content-area">
                    <!-- Input Section -->
                    <div class="input-section">
                        <div class="input-group">
                            <label for="barcode-data">بيانات الباركود:</label>
                            <textarea id="barcode-data" placeholder="أدخل البيانات هنا..."></textarea>
                        </div>

                        <div class="batch-input" id="batch-input-section" style="display: none;">
                            <label for="batch-data">بيانات دفعية (سطر لكل باركود):</label>
                            <textarea id="batch-data" placeholder="أدخل بيانات متعددة سطراً لإنشاء عدة باركود دفعة واحدة"></textarea>
                        </div>

                        <div class="mode-toggle">
                            <button id="single-mode" class="mode-btn active">فردي</button>
                            <button id="batch-mode" class="mode-btn">دفعي</button>
                        </div>

                        <div class="action-buttons">
                            <button id="generate-btn" class="btn btn-primary">
                                <i class="fas fa-magic"></i> توليد الباركود
                            </button>
                            <button id="preview-btn" class="btn btn-secondary" style="display: none;">
                                <i class="fas fa-eye"></i> معاينة
                            </button>
                        </div>
                    </div>

                    <!-- Preview Section -->
                    <div class="preview-section">
                        <h2>معاينة الباركود</h2>
                        <div class="preview-container" id="preview-container">
                            <div class="empty-preview">
                                <i class="fas fa-barcode"></i>
                                <p>سيتم عرض الباركود هنا</p>
                            </div>
                        </div>

                        <div class="export-options" id="export-options" style="display: none;">
                            <h3>خيارات التصدير</h3>
                            <div class="format-buttons">
                                <button class="export-btn" data-format="PNG">
                                    <i class="fas fa-file-image"></i> PNG
                                </button>
                                <button class="export-btn" data-format="SVG">
                                    <i class="fas fa-vector-square"></i> SVG
                                </button>
                                <button class="export-btn" data-format="PDF" id="export-pdf">
                                    <i class="fas fa-file-pdf"></i> PDF
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="app-footer">
            <p>&copy; 2025 Elashrafy Editor. جميع الحقوق محفوظة.</p>
        </footer>
    </div>

    <!-- About Modal -->
    <div class="modal" id="about-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>حول التطبيق</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <p>Elashrafy Editor هو تطبيق مكتبي احترافي لتوليد الباركود الذكي. يوفر التطبيق واجهة سهلة الاستخدام مع دعم لعدة أنواع من الباركود.</p>
                <h3>المميزات:</h3>
                <ul>
                    <li>دعم لأنواع متعددة من الباركود</li>
                    <li>خيارات تصدير متنوعة (PNG, SVG, PDF)</li>
                    <li>إمكانية توليد الباركود بشكل فردي أو دفعي</li>
                    <li>واجهة مستخدم حديثة واحترافية</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="script.js"></script>
</body>
</html>
