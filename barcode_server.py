
import eel
import barcode
from barcode.writer import ImageWriter
import os
import tempfile
import subprocess
import fitz  # PyMuPDF
from PIL import Image
import io

# Initialize Eel
eel.init('web')  # Point to the web directory

# Supported barcode formats
SUPPORTED_FORMATS = {
    'Code 128': 'code128',
    'EAN-13': 'ean13',
    'UPC-A': 'upca',
    'QR Code': 'qrcode',
    'Code 39': 'code39',
    'ITF': 'i25',
    'MSI': 'msi'
}

@eel.expose
def get_barcode_formats():
    """Return list of supported barcode formats"""
    return list(SUPPORTED_FORMATS.keys())

@eel.expose
def generate_barcode(data, format, width, height):
    """Generate a single barcode"""
    try:
        if format not in SUPPORTED_FORMATS:
            return {'success': False, 'error': f'Unsupported format: {format}'}

        if not data:
            return {'success': False, 'error': 'No data provided'}

        # Create a temporary directory for barcodes
        temp_dir = tempfile.mkdtemp()

        # Generate barcode
        format_name = SUPPORTED_FORMATS[format]

        if format == 'QR Code':
            # Special handling for QR Code
            barcode_obj = barcode.get_barcode_class(format_name)(data, writer=ImageWriter())
        else:
            barcode_obj = barcode.get_barcode_class(format_name)(data, writer=ImageWriter())

        # Set barcode options
        options = {
            'module_width': width / 100,  # Convert from 0-100 scale to actual width
            'module_height': height,
            'quiet_zone': 6.5,
            'text_distance': 5.0,
            'text': '',
            'font_size': 10,
            'background': 'white',
            'foreground': 'black',
            'text_line_align': 'center',
            'text_pad': None
        }

        # Generate filename
        filename = f"{format.replace(' ', '_')}_{data}.png"
        filepath = os.path.join(temp_dir, filename)

        # Save barcode
        barcode_obj.save(filepath, options)

        return {
            'success': True,
            'path': filepath,
            'format': format,
            'data': data
        }
    except Exception as e:
        print(f"Error generating barcode: {str(e)}")
        return {'success': False, 'error': str(e)}

@eel.expose
def generate_batch_barcode(data_lines, format, width, height):
    """Generate multiple barcodes"""
    results = []
    temp_dir = tempfile.mkdtemp()

    try:
        for i, data in enumerate(data_lines):
            if not data.strip():
                results.append({'success': False, 'error': 'Empty data line'})
                continue

            try:
                format_name = SUPPORTED_FORMATS.get(format)
                if not format_name:
                    results.append({'success': False, 'error': f'Unsupported format: {format}'})
                    continue

                # Generate barcode
                barcode_class = barcode.get_barcode_class(format_name)
                barcode_obj = barcode_class(data, writer=ImageWriter())

                # Set barcode options
                options = {
                    'module_width': width / 100,  # Convert from 0-100 scale to actual width
                    'module_height': height,
                    'quiet_zone': 6.5,
                    'text_distance': 5.0,
                    'text': '',
                    'font_size': 10,
                    'background': 'white',
                    'foreground': 'black',
                    'text_line_align': 'center',
                    'text_pad': None
                }

                # Generate filename
                filename = f"{format.replace(' ', '_')}_{i+1}.png"
                filepath = os.path.join(temp_dir, filename)

                # Save barcode
                barcode_obj.save(filepath, options)

                results.append({
                    'success': True,
                    'path': filepath,
                    'format': format,
                    'data': data
                })
            except Exception as e:
                results.append({'success': False, 'error': str(e)})

        return results
    except Exception as e:
        print(f"Error generating batch barcodes: {str(e)}")
        return [{'success': False, 'error': str(e)}]

@eel.expose
def export_to_pdf(paths):
    """Export barcodes to PDF"""
    try:
        if not paths:
            return {'success': False, 'error': 'No paths provided'}

        # Create a temporary PDF
        temp_dir = tempfile.mkdtemp()
        pdf_path = os.path.join(temp_dir, "barcodes.pdf")

        # Create PDF document
        doc = fitz.open()  # New empty PDF
        page = doc.new_page()  # New page

        # Position variables
        x0, y0 = 50, 50  # Starting position
        width, height = 200, 100  # Barcode dimensions
        spacing = 20  # Spacing between barcodes

        # Add each barcode to the PDF
        for i, path in enumerate(paths):
            if not os.path.exists(path):
                continue

            try:
                # Open image
                img = Image.open(path)

                # Calculate position for this barcode
                x = x0 + (i % 2) * (width + spacing)
                y = y0 + (i // 2) * (height + spacing)

                # Insert image into PDF
                page.insert_image(
                    fitz.Rect(x, y, x + width, y + height),
                    filename=path
                )
            except Exception as e:
                print(f"Error adding image {path} to PDF: {str(e)}")
                continue

        # Save PDF
        doc.save(pdf_path)
        doc.close()

        return {
            'success': True,
            'path': pdf_path
        }
    except Exception as e:
        print(f"Error exporting to PDF: {str(e)}")
        return {'success': False, 'error': str(e)}

if __name__ == '__main__':
    # Start the Eel application
    eel.start('index.html', size=(800, 600), port=0)
