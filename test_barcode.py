#!/usr/bin/env python3
"""
Test script to verify barcode generation functionality
"""

import sys
import os

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main import generate_barcode, SUPPORTED_FORMATS

def test_barcode_generation():
    """Test barcode generation for all supported formats"""
    test_data = "123456789"
    
    print("Testing barcode generation...")
    print(f"Supported formats: {list(SUPPORTED_FORMATS.keys())}")
    print("-" * 50)
    
    for format_name in SUPPORTED_FORMATS.keys():
        print(f"Testing {format_name}...")
        
        try:
            result = generate_barcode(test_data, format_name, width=2, height=100, format='PNG')
            
            if result['success']:
                print(f"✓ {format_name}: SUCCESS - File saved to {result['path']}")
                
                # Check if file exists
                if os.path.exists(result['path']):
                    file_size = os.path.getsize(result['path'])
                    print(f"  File size: {file_size} bytes")
                else:
                    print(f"  ⚠ Warning: File not found at {result['path']}")
            else:
                print(f"✗ {format_name}: FAILED - {result['error']}")
                
        except Exception as e:
            print(f"✗ {format_name}: ERROR - {str(e)}")
        
        print()

if __name__ == "__main__":
    test_barcode_generation()
