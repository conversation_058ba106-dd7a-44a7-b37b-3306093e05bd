
import eel
import os
import sys
import qrcode
from barcode.codex import Code128, Code39
from barcode.ean import EAN13, EAN8
from barcode.upc import UPCA
from barcode.itf import ITF
from barcode.writer import ImageWriter, SVGWriter
from PIL import Image
import io
import base64
import json
import uuid
from reportlab.graphics import renderPDF
from reportlab.graphics.shapes import Drawing
from reportlab.lib import colors
import tempfile
import shutil

# Initialize Eel
eel.init('web')

# Define supported barcode formats
SUPPORTED_FORMATS = {
    'QR Code': None,  # Special handling for QR codes
    'Code 128': Code128,
    'EAN-13': EAN13,
    'EAN-8': EAN8,
    'UPC-A': UPCA,
    'Code 39': Code39,
    'ITF': ITF
}

# Create a temporary directory for generated files
TEMP_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'temp')
if not os.path.exists(TEMP_DIR):
    os.makedirs(TEMP_DIR)

@eel.expose
def generate_barcode(data, format_type, width=2, height=100, format='PNG'):
    """Generate a barcode and return it as a base64 encoded string"""
    try:
        # Validate input
        if not data or not format_type:
            return {'success': False, 'error': 'Data and format are required'}

        if format_type not in SUPPORTED_FORMATS:
            return {'success': False, 'error': f'Unsupported format: {format_type}'}

        # Generate unique filename
        unique_id = str(uuid.uuid4())
        temp_file_path = os.path.join(TEMP_DIR, f"{unique_id}")

        if format_type == 'QR Code':
            # Generate QR Code
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(data)
            qr.make(fit=True)

            img = qr.make_image(fill_color="black", back_color="white")

            if format == 'PNG':
                img.save(f"{temp_file_path}.png")
                return {'success': True, 'path': f"{temp_file_path}.png", 'format': 'PNG'}
            elif format == 'SVG':
                # For SVG format, we need to use a different approach
                img_svg = qrcode.make(data, version=1, box_size=10, border=4)
                img_svg.save(f"{temp_file_path}.svg")
                return {'success': True, 'path': f"{temp_file_path}.svg", 'format': 'SVG'}
            else:
                return {'success': False, 'error': f'Unsupported format: {format}'}
        else:
            # Generate other barcode formats
            barcode_class = SUPPORTED_FORMATS[format_type]

            if barcode_class is None:
                return {'success': False, 'error': f'Unsupported format: {format_type}'}

            if format == 'PNG':
                barcode_instance = barcode_class(data, writer=ImageWriter())
                barcode_instance.save(temp_file_path)
                return {'success': True, 'path': f"{temp_file_path}.png", 'format': 'PNG'}
            elif format == 'SVG':
                barcode_instance = barcode_class(data, writer=SVGWriter())
                barcode_instance.save(temp_file_path)
                return {'success': True, 'path': f"{temp_file_path}.svg", 'format': 'SVG'}
            else:
                return {'success': False, 'error': f'Unsupported format: {format}'}

    except Exception as e:
        return {'success': False, 'error': str(e)}

@eel.expose
def generate_batch_barcode(data_list, format_type, width=2, height=100, format='PNG'):
    """Generate multiple barcodes and return their paths"""
    results = []

    try:
        for data in data_list:
            result = generate_barcode(data, format_type, width, height, format)
            results.append(result)

        return results
    except Exception as e:
        return {'success': False, 'error': str(e)}

@eel.expose
def export_to_pdf(barcode_paths, output_filename='barcode_export'):
    """Export multiple barcodes to a single PDF file"""
    try:
        if not barcode_paths:
            return {'success': False, 'error': 'No barcode paths provided'}

        # Create a PDF document
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter

        output_path = os.path.join(TEMP_DIR, f"{output_filename}.pdf")
        c = canvas.Canvas(output_path, pagesize=letter)

        # Position variables
        x = 50
        y = 700
        page_height = 800
        margin = 20

        for path in barcode_paths:
            if os.path.exists(path):
                # Add barcode image to PDF
                c.drawImage(path, x, y, width=200, height=100)
                y -= 120  # Move down for next barcode

                # Check if we need a new page
                if y < margin:
                    c.showPage()
                    y = page_height - margin

        c.save()
        return {'success': True, 'path': output_path}

    except Exception as e:
        return {'success': False, 'error': str(e)}

@eel.expose
def get_barcode_formats():
    """Get the list of supported barcode formats"""
    return SUPPORTED_FORMATS

@eel.expose
def get_file_as_base64(file_path):
    """Get file content as base64 string for display in browser"""
    try:
        if not os.path.exists(file_path):
            return {'success': False, 'error': 'File not found'}

        with open(file_path, 'rb') as f:
            file_content = f.read()
            base64_content = base64.b64encode(file_content).decode('utf-8')

            # Determine MIME type based on file extension
            if file_path.endswith('.png'):
                mime_type = 'image/png'
            elif file_path.endswith('.svg'):
                mime_type = 'image/svg+xml'
            elif file_path.endswith('.pdf'):
                mime_type = 'application/pdf'
            else:
                mime_type = 'application/octet-stream'

            return {
                'success': True,
                'content': base64_content,
                'mime_type': mime_type
            }
    except Exception as e:
        return {'success': False, 'error': str(e)}

@eel.expose
def open_file(file_path):
    """Open file with default system application"""
    try:
        if not os.path.exists(file_path):
            return {'success': False, 'error': 'File not found'}

        if sys.platform.startswith('win'):
            os.startfile(file_path)
        elif sys.platform.startswith('darwin'):
            os.system(f'open "{file_path}"')
        else:
            os.system(f'xdg-open "{file_path}"')

        return {'success': True}
    except Exception as e:
        return {'success': False, 'error': str(e)}

@eel.expose
def cleanup_temp_files():
    """Clean up temporary files"""
    try:
        for filename in os.listdir(TEMP_DIR):
            file_path = os.path.join(TEMP_DIR, filename)
            try:
                if os.path.isfile(file_path):
                    os.unlink(file_path)
            except Exception as e:
                print(f"Error deleting file {file_path}: {e}")
        return {'success': True}
    except Exception as e:
        return {'success': False, 'error': str(e)}

# Start the application
if __name__ == '__main__':
    # Clean up temp files on startup
    cleanup_temp_files()

    # Start the Eel application
    eel.start('index.html', size=(1000, 700), port=0)
