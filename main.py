
import eel
import os
import sys
import qrcode
from barcode.codex import Code128, Code39
from barcode.ean import EAN13, EAN8
from barcode.upc import UPCA
from barcode.itf import ITF
from barcode.writer import ImageWriter, SVGWriter
from PIL import Image, ImageDraw, ImageFont
import io
import base64
import json
import uuid
from reportlab.graphics import renderPDF
from reportlab.graphics.shapes import Drawing
from reportlab.lib import colors
import tempfile
import shutil

# Initialize Eel
eel.init('web')

# Define supported barcode formats
SUPPORTED_FORMATS = {
    'QR Code': None,  # Special handling for QR codes
    'Code 128': Code128,
    'EAN-13': EAN13,
    'EAN-8': EAN8,
    'UPC-A': UPCA,
    'Code 39': Code39,
    'ITF': ITF
}

# Create a temporary directory for generated files
TEMP_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'temp')
if not os.path.exists(TEMP_DIR):
    os.makedirs(TEMP_DIR)

@eel.expose
def generate_barcode(data, format_type, width=2, height=100, format='PNG'):
    """Generate a barcode and return it as a base64 encoded string"""
    try:
        # Validate input
        if not data or not format_type:
            return {'success': False, 'error': 'Data and format are required'}

        if format_type not in SUPPORTED_FORMATS:
            return {'success': False, 'error': f'Unsupported format: {format_type}'}

        # Generate unique filename
        unique_id = str(uuid.uuid4())
        temp_file_path = os.path.join(TEMP_DIR, f"{unique_id}")

        if format_type == 'QR Code':
            # Generate QR Code
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=int(width * 5),  # Scale box size with width
                border=4,
            )
            qr.add_data(data)
            qr.make(fit=True)

            img = qr.make_image(fill_color="black", back_color="white")

            # Add text below QR code
            img_with_text = add_text_to_image(img, data, format_type)

            if format == 'PNG':
                img_with_text.save(f"{temp_file_path}.png")
                return {'success': True, 'path': f"{temp_file_path}.png", 'format': 'PNG'}
            elif format == 'SVG':
                img_with_text.save(f"{temp_file_path}.png")  # Save as PNG for now
                return {'success': True, 'path': f"{temp_file_path}.png", 'format': 'PNG'}
            else:
                return {'success': False, 'error': f'Unsupported format: {format}'}
        else:
            # Generate other barcode formats
            barcode_class = SUPPORTED_FORMATS[format_type]

            if barcode_class is None:
                return {'success': False, 'error': f'Unsupported format: {format_type}'}

            # Create custom writer with options
            writer_options = {
                'module_width': width * 0.2,  # Scale width
                'module_height': height * 0.8,  # Scale height
                'quiet_zone': 6.5,
                'text_distance': 5.0,
                'font_size': 10,
                'text': data,  # Show the data as text
                'write_text': True
            }

            if format == 'PNG':
                writer = ImageWriter()
                writer.set_options(writer_options)
                barcode_instance = barcode_class(data, writer=writer)
                barcode_instance.save(temp_file_path)
                return {'success': True, 'path': f"{temp_file_path}.png", 'format': 'PNG'}
            elif format == 'SVG':
                writer = SVGWriter()
                writer.set_options(writer_options)
                barcode_instance = barcode_class(data, writer=writer)
                barcode_instance.save(temp_file_path)
                return {'success': True, 'path': f"{temp_file_path}.svg", 'format': 'SVG'}
            else:
                return {'success': False, 'error': f'Unsupported format: {format}'}

    except Exception as e:
        return {'success': False, 'error': str(e)}

def add_text_to_image(img, text, format_type):
    """Add text below an image"""
    try:
        # Convert to RGB if needed
        if img.mode != 'RGB':
            img = img.convert('RGB')

        # Create a new image with extra space for text
        img_width, img_height = img.size
        text_height = 40
        new_img = Image.new('RGB', (img_width, img_height + text_height), 'white')

        # Paste the original image
        new_img.paste(img, (0, 0))

        # Add text
        draw = ImageDraw.Draw(new_img)

        # Try to use a font, fallback to default if not available
        try:
            font = ImageFont.truetype("arial.ttf", 12)
        except:
            font = ImageFont.load_default()

        # Calculate text position (centered)
        text_bbox = draw.textbbox((0, 0), text, font=font)
        text_width = text_bbox[2] - text_bbox[0]
        text_x = (img_width - text_width) // 2
        text_y = img_height + 10

        # Draw text
        draw.text((text_x, text_y), text, fill='black', font=font)

        # Add format type label
        format_text = f"({format_type})"
        format_bbox = draw.textbbox((0, 0), format_text, font=font)
        format_width = format_bbox[2] - format_bbox[0]
        format_x = (img_width - format_width) // 2
        format_y = text_y + 15

        draw.text((format_x, format_y), format_text, fill='gray', font=font)

        return new_img
    except Exception as e:
        print(f"Error adding text to image: {e}")
        return img

@eel.expose
def generate_batch_barcode(data_list, format_type, width=2, height=100, format='PNG'):
    """Generate multiple barcodes and return their paths"""
    results = []

    try:
        for data in data_list:
            result = generate_barcode(data, format_type, width, height, format)
            results.append(result)

        return results
    except Exception as e:
        return {'success': False, 'error': str(e)}

@eel.expose
def export_to_pdf(barcode_paths, output_filename='barcode_export'):
    """Export multiple barcodes to a single PDF file"""
    try:
        if not barcode_paths:
            return {'success': False, 'error': 'No barcode paths provided'}

        # Create a PDF document
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter, A4
        from reportlab.lib.units import inch
        import datetime

        output_path = os.path.join(TEMP_DIR, f"{output_filename}_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf")
        c = canvas.Canvas(output_path, pagesize=A4)

        # Page dimensions
        page_width, page_height = A4
        margin = 50

        # Header
        c.setFont("Helvetica-Bold", 16)
        c.drawString(margin, page_height - margin, "Elashrafy Editor - Barcode Export")
        c.setFont("Helvetica", 10)
        c.drawString(margin, page_height - margin - 20, f"Generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # Position variables
        x = margin
        y = page_height - margin - 60
        barcode_width = 300
        barcode_height = 120
        spacing = 40

        for i, path in enumerate(barcode_paths):
            if os.path.exists(path):
                # Check if we need a new page
                if y < margin + barcode_height:
                    c.showPage()
                    # Add header to new page
                    c.setFont("Helvetica-Bold", 16)
                    c.drawString(margin, page_height - margin, "Elashrafy Editor - Barcode Export (continued)")
                    y = page_height - margin - 60

                # Add barcode image to PDF
                try:
                    c.drawImage(path, x, y - barcode_height, width=barcode_width, height=barcode_height, preserveAspectRatio=True)

                    # Add barcode number
                    c.setFont("Helvetica", 8)
                    c.drawString(x, y - barcode_height - 15, f"Barcode #{i + 1}")

                    y -= barcode_height + spacing
                except Exception as img_error:
                    print(f"Error adding image {path} to PDF: {img_error}")
                    # Add error text instead
                    c.setFont("Helvetica", 10)
                    c.drawString(x, y - 20, f"Error loading barcode #{i + 1}: {os.path.basename(path)}")
                    y -= 40

        # Add footer
        c.setFont("Helvetica", 8)
        c.drawString(margin, 30, f"Total barcodes: {len(barcode_paths)} | Generated by Elashrafy Editor")

        c.save()
        return {'success': True, 'path': output_path}

    except Exception as e:
        return {'success': False, 'error': str(e)}

@eel.expose
def get_barcode_formats():
    """Get the list of supported barcode formats"""
    return SUPPORTED_FORMATS

@eel.expose
def get_file_as_base64(file_path):
    """Get file content as base64 string for display in browser"""
    try:
        if not os.path.exists(file_path):
            return {'success': False, 'error': 'File not found'}

        with open(file_path, 'rb') as f:
            file_content = f.read()
            base64_content = base64.b64encode(file_content).decode('utf-8')

            # Determine MIME type based on file extension
            if file_path.endswith('.png'):
                mime_type = 'image/png'
            elif file_path.endswith('.svg'):
                mime_type = 'image/svg+xml'
            elif file_path.endswith('.pdf'):
                mime_type = 'application/pdf'
            else:
                mime_type = 'application/octet-stream'

            return {
                'success': True,
                'content': base64_content,
                'mime_type': mime_type
            }
    except Exception as e:
        return {'success': False, 'error': str(e)}

@eel.expose
def open_file(file_path):
    """Open file with default system application"""
    try:
        if not os.path.exists(file_path):
            return {'success': False, 'error': 'File not found'}

        if sys.platform.startswith('win'):
            os.startfile(file_path)
        elif sys.platform.startswith('darwin'):
            os.system(f'open "{file_path}"')
        else:
            os.system(f'xdg-open "{file_path}"')

        return {'success': True}
    except Exception as e:
        return {'success': False, 'error': str(e)}

@eel.expose
def cleanup_temp_files():
    """Clean up temporary files"""
    try:
        for filename in os.listdir(TEMP_DIR):
            file_path = os.path.join(TEMP_DIR, filename)
            try:
                if os.path.isfile(file_path):
                    os.unlink(file_path)
            except Exception as e:
                print(f"Error deleting file {file_path}: {e}")
        return {'success': True}
    except Exception as e:
        return {'success': False, 'error': str(e)}

# Start the application
if __name__ == '__main__':
    # Clean up temp files on startup
    cleanup_temp_files()

    # Start the Eel application
    eel.start('index.html', size=(1000, 700), port=0)
