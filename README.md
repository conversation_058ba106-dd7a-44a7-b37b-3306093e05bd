
# Elashrafy Editor - مولِّد الباركود الذكي

تطبيق مكتبي احترافي لتوليد الباركود الذكي باستخدام بايثون وواجهة ويب حديثة.

## المميزات

- دعم لأنواع متعددة من الباركود (QR Code، Code 128، EAN-13، إلخ)
- واجهة مستخدم عربية احترافية وبسيطة
- خيارات تصدير متعددة (PNG، SVG، PDF)
- إمكانية توليد الباركود بشكل فردي أو دفعي
- تصميم متجايعمل على مختلف أحجام الشاشات
- إمكانية تخصيص أبعاد الباركود

## المتطلبات

- Python 3.7 أو أحدث
- نظام التشغيل Windows

## التثبيت

1. قم بتنزيل الملفات من المستودع
2. افتح موجه الأوامر (Command Prompt) في المجلد الرئيسي للمشروع
3. قم بتثبيت المكتبات المطلوبة باستخدام الأمر:
   ```
   pip install -r requirements.txt
   ```

## الاستخدام

1. قم بتشغيل التطبيق باستخدام الأمر:
   ```
   python main.py
   ```
2. سيتم فتح نافذة التطبيق
3. اختر نوع الباركود من القائمة الجانبية
4. أدخل البيانات في حقل الإدخال
5. قم بتخصيص أبعاد الباركود إذا رغبت
6. انقر على زر "توليد الباركود"
7. بعد التوليد، يمكنك معاينة الباركود وتصديره للصيغة المطلوبة

## الهيكل

```
Elashrafy Editor/
├── main.py              # الملف الرئيسي لتشغيل التطبيق
├── requirements.txt     # قائمة المكتبات المطلوبة
├── README.md            # ملف التعليمات
├── temp/                # مجلد مؤقت لتخزين المولدات
└── web/                 # مجلد ملفات الواجهة
    ├── index.html       # هيكل صفحة الويب
    ├── style.css        # التنسيقات
    └── script.js        # الوظائف التفاعلية
```

## المشاكل الشائعة

### مشكلة: لا يتم فتح نافذة التطبيق

- تأكد من تثبيت جميع المكتبات المطلوبة
- تأكد من أن ملفات الويب موجودة في مجلد web

### مشكلة: الباركولات غير واضحة

- تأكد من إدخال البيانات بشكل صحيح
- جرب تغيير أبعاد الباركود

## الترخيص

هذا المشروع مفتوح المصدر ويمكن استخدامه بحرية.

## المساهمة

للمساهمة في التطوير، يمكنك:

1. إنشاء نسخة جديدة من المشروع
2. إضافة تحسينات جديدة
3. الإبلاغ عن المشاكل

## التواصل

لأي استفسارات، يرجى التواصل عبر البريد الإلكتروني.

---

جميع الحقوق محفوظة © 2023 Elashrafy Editor
